import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus } from 'lucide-react';
import { Trial } from '@/types';
import { toast } from 'sonner';
import { mockSponsors } from '@/data/mockData';

interface CreateTrialDialogProps {
  onCreateTrial: (trial: Trial) => void;
}

const CreateTrialDialog = ({ onCreateTrial }: CreateTrialDialogProps) => {
  const [open, setOpen] = useState(false);
  const [trialName, setTrialName] = useState('');
  const [selectedSponsor, setSelectedSponsor] = useState('');

  const handleCreate = () => {
    if (!trialName.trim()) {
      toast.error('Trial name is required');
      return;
    }

    if (!selectedSponsor) {
      toast.error('Please select a sponsor');
      return;
    }

    const newTrial: Trial = {
      id: Date.now().toString(),
      name: trialName.trim(),
      sponsor: selectedSponsor,
      patients: [],
      priceList: []
    };

    onCreateTrial(newTrial);
    toast.success('Trial created successfully');

    // Reset form
    setTrialName('');
    setSelectedSponsor('');
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button className="flex items-center space-x-2">
          <Plus className="h-4 w-4" />
          <span>Create New Trial</span>
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Trial</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="trialName">Trial Name</Label>
            <Input
              id="trialName"
              value={trialName}
              onChange={(e) => setTrialName(e.target.value)}
              placeholder="Enter trial name"
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="sponsor">Sponsor</Label>
            <Select value={selectedSponsor} onValueChange={setSelectedSponsor}>
              <SelectTrigger>
                <SelectValue placeholder="Select a sponsor" />
              </SelectTrigger>
              <SelectContent>
                {mockSponsors.map(sponsor => (
                  <SelectItem key={sponsor.id} value={sponsor.name}>
                    {sponsor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div className="flex justify-end space-x-2 pt-4">
            <Button variant="outline" onClick={() => setOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreate}>
              Create Trial
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default CreateTrialDialog;
