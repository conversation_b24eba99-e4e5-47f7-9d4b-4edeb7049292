import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { mockEmployees, mockRoles, mockSponsors } from '@/data/mockData';
import { Employee, Role, ManageableItem } from '@/types';
import RoleManager from '@/components/RoleManager';
import EmployeeManager from '@/components/EmployeeManager';
import SponsorManager from '@/components/SponsorManager';

const Settings = () => {
  const [sponsors, setSponsors] = useState<ManageableItem[]>(mockSponsors);
  const [employees, setEmployees] = useState<Employee[]>(mockEmployees);
  const [roles, setRoles] = useState<Role[]>(mockRoles);

  return (
    <Layout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Settings</h1>

        <Tabs defaultValue="sponsors" className="space-y-4">
          <TabsList>
            <TabsTrigger value="sponsors">Sponsors</TabsTrigger>
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="roles">Roles</TabsTrigger>
          </TabsList>

          <TabsContent value="sponsors">
            <SponsorManager sponsors={sponsors} onUpdateSponsors={setSponsors} />
          </TabsContent>

          <TabsContent value="employees">
            <EmployeeManager employees={employees} roles={roles} onUpdateEmployees={setEmployees} />
          </TabsContent>

          <TabsContent value="roles">
            <RoleManager roles={roles} onUpdateRoles={setRoles} />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Settings;
