
import React, { useState } from 'react';
import Layout from '@/components/Layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Plus, Trash2, Edit, Save, X } from 'lucide-react';
import { mockEmployees, mockRoles } from '@/data/mockData';
import { Employee, Role } from '@/types';
import RoleManager from '@/components/RoleManager';
import { toast } from 'sonner';

interface ManageableItem {
  id: string;
  name: string;
}

const Settings = () => {
  const [sponsors, setSponsors] = useState<ManageableItem[]>([
    { id: '1', name: 'Acme Corp' },
    { id: '2', name: 'TechStart Inc' },
    { id: '3', name: 'Global Solutions' },
  ]);

  const [employees, setEmployees] = useState<Employee[]>(mockEmployees);
  const [roles, setRoles] = useState<Role[]>(mockRoles);

  const [editingItem, setEditingItem] = useState<{ type: string; id: string; name: string } | null>(null);
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null);
  const [newItemName, setNewItemName] = useState('');

  const handleAdd = (type: 'sponsors', name: string) => {
    if (!name.trim()) {
      toast.error('Name cannot be empty');
      return;
    }

    const newItem: ManageableItem = {
      id: Date.now().toString(),
      name: name.trim(),
    };

    if (type === 'sponsors') {
      setSponsors([...sponsors, newItem]);
    }

    setNewItemName('');
    toast.success(`${type.slice(0, -1)} added successfully`);
  };

  const handleAddEmployee = () => {
    if (!newItemName.trim()) {
      toast.error('Main Researcher name cannot be empty');
      return;
    }

    const newEmployee: Employee = {
      id: Date.now().toString(),
      name: newItemName.trim(),
    };

    setEmployees([...employees, newEmployee]);
    setNewItemName('');
    toast.success('Employee added successfully');
  };

  const handleEdit = (type: string, id: string, currentName: string) => {
    setEditingItem({ type, id, name: currentName });
  };

  const handleEditEmployee = (employee: Employee) => {
    if (!employee || !employee.id) {
      toast.error('Invalid employee data');
      return;
    }
    setEditingEmployee({ ...employee });
  };

  const handleSaveEdit = () => {
    if (!editingItem || !editingItem.name.trim()) {
      toast.error('Name cannot be empty');
      return;
    }

    const updatedName = editingItem.name.trim();

    if (editingItem.type === 'sponsors') {
      setSponsors(sponsors.map(item =>
        item.id === editingItem.id ? { ...item, name: updatedName } : item
      ));
    }

    setEditingItem(null);
    toast.success(`${editingItem.type.slice(0, -1)} updated successfully`);
  };

  const handleSaveEmployeeEdit = () => {
    if (!editingEmployee || !editingEmployee.name.trim()) {
      toast.error('Employee name cannot be empty');
      return;
    }

    const updatedEmployees = employees.map(emp =>
      emp.id === editingEmployee.id ? { ...editingEmployee, name: editingEmployee.name.trim() } : emp
    );

    setEmployees(updatedEmployees);
    setEditingEmployee(null);
    toast.success('Employee updated successfully');
  };

  const handleDelete = (type: 'sponsors', id: string) => {
    if (type === 'sponsors') {
      setSponsors(sponsors.filter(item => item.id !== id));
    }

    toast.success(`${type.slice(0, -1)} deleted successfully`);
  };

  const handleDeleteEmployee = (id: string) => {
    setEmployees(employees.filter(emp => emp.id !== id));
    toast.success('Employee deleted successfully');
  };

  const getRoleName = (roleId?: string) => {
    if (!roleId) return 'No role assigned';
    if (!roles || roles.length === 0) return 'No roles available';
    const role = roles.find(r => r && r.id === roleId);
    return role ? role.name : 'Unknown role';
  };

  const renderManageSection = (
    title: string,
    items: ManageableItem[],
    type: 'sponsors'
  ) => (
    <Card>
      <CardHeader>
        <CardTitle>{title}</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex space-x-2">
          <Input
            value={newItemName}
            onChange={(e) => setNewItemName(e.target.value)}
            placeholder={`Enter new ${type.slice(0, -1)} name`}
            onKeyPress={(e) => e.key === 'Enter' && handleAdd(type, newItemName)}
          />
          <Button onClick={() => handleAdd(type, newItemName)}>
            <Plus className="h-4 w-4 mr-2" />
            Add
          </Button>
        </div>

        <div className="grid gap-2">
          {items.map(item => (
            <div key={item.id} className="flex items-center justify-between p-3 border rounded-lg">
              {editingItem?.id === item.id ? (
                <div className="flex items-center space-x-2 flex-1">
                  <Input
                    value={editingItem.name}
                    onChange={(e) => setEditingItem({ ...editingItem, name: e.target.value })}
                    onKeyPress={(e) => e.key === 'Enter' && handleSaveEdit()}
                  />
                  <Button size="sm" onClick={handleSaveEdit}>
                    <Save className="h-4 w-4" />
                  </Button>
                  <Button size="sm" variant="outline" onClick={() => setEditingItem(null)}>
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ) : (
                <>
                  <span className="font-medium">{item.name}</span>
                  <div className="flex space-x-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleEdit(type, item.id, item.name)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={() => handleDelete(type, item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );

  return (
    <Layout>
      <div className="space-y-6">
        <h1 className="text-3xl font-bold">Settings</h1>

        <Tabs defaultValue="sponsors" className="space-y-4">
          <TabsList>
            <TabsTrigger value="sponsors">Sponsors</TabsTrigger>
            <TabsTrigger value="employees">Employees</TabsTrigger>
            <TabsTrigger value="roles">Roles</TabsTrigger>
          </TabsList>

          <TabsContent value="sponsors">
            {renderManageSection('Manage Sponsors', sponsors, 'sponsors')}
          </TabsContent>

          <TabsContent value="employees">
            <Card>
              <CardHeader>
                <CardTitle>Manage Employees</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex space-x-2">
                  <Input
                    value={newItemName}
                    onChange={(e) => setNewItemName(e.target.value)}
                    placeholder="Enter employee name"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddEmployee()}
                  />
                  <Button onClick={handleAddEmployee}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Employee
                  </Button>
                </div>

                <div className="grid gap-2">
                  {employees.filter(employee => employee && employee.id).map(employee => (
                    <div key={employee.id} className="flex items-center justify-between p-3 border rounded-lg">
                      {editingEmployee?.id === employee.id ? (
                        <div className="flex items-center space-x-2 flex-1">
                          <Input
                            value={editingEmployee.name}
                            onChange={(e) => setEditingEmployee({ ...editingEmployee, name: e.target.value })}
                            onKeyPress={(e) => e.key === 'Enter' && handleSaveEmployeeEdit()}
                            className="flex-1"
                          />
                          <Select
                            value={editingEmployee.roleId || ''}
                            onValueChange={(value) => setEditingEmployee({ ...editingEmployee, roleId: value || undefined })}
                          >
                            <SelectTrigger className="w-[200px]">
                              <SelectValue placeholder="Select role" />
                            </SelectTrigger>
                            <SelectContent>
                              {roles.filter(role => role.id).map(role => (
                                <SelectItem key={role.id} value={role.id}>
                                  {role.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button size="sm" onClick={handleSaveEmployeeEdit}>
                            <Save className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline" onClick={() => setEditingEmployee(null)}>
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      ) : (
                        <>
                          <div>
                            <span className="font-medium">{employee.name}</span>
                            <div className="text-sm text-gray-500">{getRoleName(employee.roleId)}</div>
                          </div>
                          <div className="flex space-x-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleEditEmployee(employee)}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleDeleteEmployee(employee.id)}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="roles">
            <RoleManager roles={roles} onUpdateRoles={setRoles} />
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
};

export default Settings;
